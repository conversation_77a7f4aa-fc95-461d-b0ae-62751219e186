// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC3Zpsn_6zTBz4OltZbbjPvNu65fOB5JSs",
  authDomain: "spck2-jsi.firebaseapp.com",
  projectId: "spck2-jsi",
  storageBucket: "spck2-jsi.firebasestorage.app",
  messagingSenderId: "1083231365754",
  appId: "1:1083231365754:web:8dfbf84933daf329b3a9c3",
  measurementId: "G-M5Z4YNBD6Q",
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const googleProvider = new firebase.auth.GoogleAuthProvider();

// Firebase Auth State Observer
auth.onAuthStateChanged((user) => {
  if (user) {
    console.log("User is signed in:", user);
    // User is signed in, redirect to dashboard or update UI
    if (
      window.location.pathname.includes("login.html") ||
      window.location.pathname.includes("register.html")
    ) {
      window.location.href = "dashboard.html";
    }
  } else {
    console.log("User is signed out");
    // User is signed out, stay on login/register page
  }
});
