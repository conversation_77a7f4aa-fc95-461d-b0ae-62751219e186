// Google Sign-In Configuration
const GOOGLE_CLIENT_ID = "YOUR_GOOGLE_CLIENT_ID_HERE"; // Thay bằng Client ID của bạn

// Khởi tạo Google Sign-In
function initializeGoogleSignIn() {
  if (typeof google !== "undefined") {
    google.accounts.id.initialize({
      client_id: GOOGLE_CLIENT_ID,
      callback: handleGoogleResponse,
    });
  }
}

// Xử lý phản hồi từ Google
function handleGoogleResponse(response) {
  console.log("Google login success:", response);

  // Gi<PERSON> lập xử lý đăng nhập thành công
  showNotification("Đăng nhập với Google thành công!", "success");

  // Chuyển hướng sau khi đăng nhập
  setTimeout(() => {
    window.location.href = "dashboard.html";
  }, 1500);
}

// Đ<PERSON>ng nhập với Google
function loginWithGoogle() {
  if (typeof google !== "undefined") {
    google.accounts.id.prompt();
  } else {
    showNotification(
      "Google Sign-In chưa được tải. Vui lòng thử lại.",
      "error"
    );
  }
}

// Đăng ký với Google
function registerWithGoogle() {
  loginWithGoogle(); // Sử dụng cùng một hàm cho cả đăng ký và đăng nhập
}

// Toggle password visibility
function togglePassword(inputId) {
  const input = document.getElementById(inputId);
  const icon = input.nextElementSibling;

  if (input.type === "password") {
    input.type = "text";
    icon.classList.remove("fa-eye-slash");
    icon.classList.add("fa-eye");
  } else {
    input.type = "password";
    icon.classList.remove("fa-eye");
    icon.classList.add("fa-eye-slash");
  }
}

// Kiểm tra độ mạnh mật khẩu
function checkPasswordStrength(password) {
  const strengthIndicator = document.getElementById("passwordStrength");
  if (!strengthIndicator) return;

  let strength = 0;

  if (password.length >= 8) strength++;
  if (/[a-z]/.test(password)) strength++;
  if (/[A-Z]/.test(password)) strength++;
  if (/[0-9]/.test(password)) strength++;
  if (/[^A-Za-z0-9]/.test(password)) strength++;

  strengthIndicator.className = "password-strength";

  if (strength < 2) {
    strengthIndicator.classList.add("weak");
  } else if (strength < 4) {
    strengthIndicator.classList.add("medium");
  } else {
    strengthIndicator.classList.add("strong");
  }
}

// Xử lý form đăng nhập
function handleLoginForm(e) {
  e.preventDefault();

  const email = document.getElementById("loginEmail").value;
  const password = document.getElementById("loginPassword").value;
  const rememberMe = document.getElementById("rememberMe").checked;

  // Validate
  if (!validateEmail(email)) {
    showNotification("Vui lòng nhập email hợp lệ", "error");
    return;
  }

  if (password.length < 6) {
    showNotification("Mật khẩu phải có ít nhất 6 ký tự", "error");
    return;
  }

  // Giả lập đăng nhập thành công
  showNotification("Đăng nhập thành công!", "success");

  // Lưu thông tin nếu chọn "Ghi nhớ"
  if (rememberMe) {
    localStorage.setItem("rememberedEmail", email);
  }

  // Chuyển hướng
  setTimeout(() => {
    window.location.href = "dashboard.html";
  }, 1500);
}

// Xử lý form đăng ký
function handleRegisterForm(e) {
  e.preventDefault();

  const name = document.getElementById("registerName").value;
  const email = document.getElementById("registerEmail").value;
  const password = document.getElementById("registerPassword").value;
  const confirmPassword = document.getElementById("confirmPassword").value;
  const agreeTerms = document.getElementById("agreeTerms").checked;

  // Validate
  if (!name.trim()) {
    showNotification("Vui lòng nhập họ tên", "error");
    return;
  }

  if (!validateEmail(email)) {
    showNotification("Vui lòng nhập email hợp lệ", "error");
    return;
  }

  if (password.length < 8) {
    showNotification("Mật khẩu phải có ít nhất 8 ký tự", "error");
    return;
  }

  if (password !== confirmPassword) {
    showNotification("Mật khẩu xác nhận không khớp", "error");
    return;
  }

  if (!agreeTerms) {
    showNotification("Bạn phải đồng ý với điều khoản dịch vụ", "error");
    return;
  }

  // Giả lập đăng ký thành công
  showNotification("Đăng ký thành công!", "success");

  // Chuyển hướng sang trang đăng nhập
  setTimeout(() => {
    window.location.href = "login.html";
  }, 1500);
}

// Validate email
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

// Hiển thị thông báo
function showNotification(message, type = "info") {
  // Tạo notification element
  const notification = document.createElement("div");
  notification.className = `notification ${type}`;
  notification.innerHTML = `
        <i class="fas ${
          type === "success" ? "fa-check-circle" : "fa-exclamation-circle"
        }"></i>
        <span>${message}</span>
    `;

  // Style cho notification
  Object.assign(notification.style, {
    position: "fixed",
    top: "20px",
    right: "20px",
    padding: "16px 24px",
    borderRadius: "8px",
    color: "white",
    fontSize: "14px",
    fontWeight: "500",
    zIndex: "1000",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    animation: "slideIn 0.3s ease-out",
    backgroundColor: type === "success" ? "#48bb78" : "#f56565",
  });

  document.body.appendChild(notification);

  // Tự động ẩn sau 3 giây
  setTimeout(() => {
    notification.style.animation = "slideOut 0.3s ease-out";
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Loading state cho button
function setButtonLoading(button, loading = true) {
  if (loading) {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
  } else {
    button.disabled = false;
    button.innerHTML = button.dataset.originalText || button.innerHTML;
  }
}

// Auto-fill remembered email
function autoFillRememberedEmail() {
  const rememberedEmail = localStorage.getItem("rememberedEmail");
  if (rememberedEmail && document.getElementById("loginEmail")) {
    document.getElementById("loginEmail").value = rememberedEmail;
    document.getElementById("rememberMe").checked = true;
  }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Khởi tạo Google Sign-In
  initializeGoogleSignIn();

  // Auto-fill email đã lưu
  autoFillRememberedEmail();

  // Password strength checker
  const passwordInput = document.getElementById("registerPassword");
  if (passwordInput) {
    passwordInput.addEventListener("input", function () {
      checkPasswordStrength(this.value);
    });
  }

  // Form đăng nhập
  const loginForm = document.getElementById("loginForm");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLoginForm);
  }

  // Form đăng ký
  const registerForm = document.getElementById("registerForm");
  if (registerForm) {
    registerForm.addEventListener("submit", handleRegisterForm);
  }
});

// CSS animation cho notification
const style = document.createElement("style");
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
