/* Reset và biến toàn c<PERSON>c */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --bg-primary: #f7fafc;
  --bg-secondary: #ffffff;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-primary);
}

.container {
  width: 100%;
  max-width: 420px;
}

.form-wrapper {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h1 {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.form-header p {
  color: var(--text-secondary);
  font-size: 16px;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.input-field {
  position: relative;
  display: flex;
  align-items: center;
}

.input-field i {
  position: absolute;
  left: 16px;
  color: var(--text-secondary);
  font-size: 16px;
  z-index: 1;
}

.input-field input {
  width: 100%;
  padding: 14px 16px 14px 48px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  transition: var(--transition);
  background: var(--bg-secondary);
}

.input-field input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-field input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.toggle-password {
  cursor: pointer;
  left: auto !important;
  right: 16px;
  transition: var(--transition);
}

.toggle-password:hover {
  color: var(--primary-color);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  margin-right: 8px;
  transition: var(--transition);
  position: relative;
}

.checkbox-container input:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  color: white;
  font-size: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.forgot-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.forgot-link:hover {
  color: var(--primary-dark);
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-google {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.btn-google:hover {
  background: var(--bg-primary);
  border-color: var(--primary-color);
}

.btn-google img {
  width: 20px;
  height: 20px;
}

.divider {
  text-align: center;
  position: relative;
  margin: 16px 0;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

.divider span {
  background: var(--bg-secondary);
  padding: 0 16px;
  color: var(--text-secondary);
  font-size: 14px;
  position: relative;
}

.form-footer {
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.link-primary {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.link-primary:hover {
  color: var(--primary-dark);
}

.terms-group {
  margin: 8px 0;
}

.password-strength {
  margin-top: 8px;
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
  background: var(--border-color);
}

.password-strength span {
  display: block;
  height: 100%;
  width: 0;
  transition: var(--transition);
  border-radius: 2px;
}

.password-strength.weak span {
  width: 33%;
  background: var(--error-color);
}

.password-strength.medium span {
  width: 66%;
  background: var(--warning-color);
}

.password-strength.strong span {
  width: 100%;
  background: var(--success-color);
}

/* Responsive Design */
@media (max-width: 480px) {
  .form-wrapper {
    padding: 30px 20px;
    margin: 10px;
  }

  .form-header h1 {
    font-size: 24px;
  }

  .form-header p {
    font-size: 14px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-wrapper {
  animation: fadeIn 0.5s ease-out;
}

/* Loading state */
.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}
